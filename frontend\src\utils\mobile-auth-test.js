/**
 * 移动端认证测试工具
 * 用于测试移动端登录状态和token刷新功能
 */
import { useUserStore } from '@/store/mobile/user'
import { mobileRequest } from '@/utils/mobileRequest'

// 创建测试工具对象
const mobileAuthTest = {
  
  /**
   * 模拟登录
   */
  async login() {
    console.log('🔐 开始模拟登录...')
    try {
      const userStore = useUserStore()
      await userStore.login()
      console.log('✅ 登录成功')
      console.log('用户信息:', userStore.userInfo)
      console.log('Access Token:', userStore.accessToken)
    } catch (error) {
      console.error('❌ 登录失败:', error)
    }
  },

  /**
   * 模拟登出
   */
  logout() {
    console.log('🚪 开始登出...')
    const userStore = useUserStore()
    userStore.clearUserInfo()
    console.log('✅ 登出成功')
  },

  /**
   * 检查登录状态
   */
  check() {
    console.log('🔍 检查登录状态...')
    const userStore = useUserStore()
    console.log('登录状态:', userStore.isLogin)
    console.log('用户信息:', userStore.userInfo)
    console.log('Access Token:', userStore.accessToken ? '存在' : '不存在')
    console.log('Refresh Token:', userStore.refreshToken ? '存在' : '不存在')
    
    // 检查localStorage
    const storedUserInfo = localStorage.getItem('userInfo')
    console.log('localStorage中的用户信息:', storedUserInfo ? '存在' : '不存在')
    
    return userStore.isLogin
  },

  /**
   * 测试API调用
   */
  async testApi() {
    console.log('🌐 测试API调用...')
    try {
      // 测试一个需要认证的API
      const response = await mobileRequest({
        url: '/blade-system/dict-biz/dictionary',
        method: 'get',
        params: 'wel_config'
      })
      console.log('✅ API调用成功:', response.data)
    } catch (error) {
      console.error('❌ API调用失败:', error.message)
    }
  },

  /**
   * 测试token刷新
   */
  async testRefresh() {
    console.log('🔄 测试token刷新...')
    try {
      const userStore = useUserStore()
      const oldToken = userStore.accessToken
      console.log('旧token:', oldToken)
      
      const newToken = await userStore.refreshAccessToken()
      console.log('新token:', newToken)
      console.log('✅ token刷新成功')
    } catch (error) {
      console.error('❌ token刷新失败:', error.message)
    }
  },

  /**
   * 模拟token过期
   */
  expireToken() {
    console.log('⏰ 模拟token过期...')
    const userStore = useUserStore()
    // 将token设置为无效值
    userStore.accessToken = 'expired_token'
    console.log('✅ token已设置为过期状态')
  },

  /**
   * 运行完整测试
   */
  async test() {
    console.log('🧪 开始完整测试...')
    console.log('='.repeat(50))
    
    // 1. 检查初始状态
    console.log('1. 检查初始状态')
    this.check()
    console.log('-'.repeat(30))
    
    // 2. 测试API调用（可能会触发登录）
    console.log('2. 测试API调用')
    await this.testApi()
    console.log('-'.repeat(30))
    
    // 3. 再次检查状态
    console.log('3. 再次检查状态')
    this.check()
    console.log('-'.repeat(30))
    
    // 4. 测试token刷新
    console.log('4. 测试token刷新')
    await this.testRefresh()
    console.log('-'.repeat(30))
    
    // 5. 模拟token过期并测试自动刷新
    console.log('5. 测试token过期处理')
    this.expireToken()
    await this.testApi()
    console.log('-'.repeat(30))
    
    console.log('✅ 完整测试结束')
    console.log('='.repeat(50))
  },

  /**
   * 清除所有数据
   */
  clear() {
    console.log('🧹 清除所有数据...')
    localStorage.removeItem('userInfo')
    const userStore = useUserStore()
    userStore.clearUserInfo()
    console.log('✅ 数据清除完成')
  }
}

// 将测试工具挂载到全局对象
if (typeof window !== 'undefined') {
  window.mobileAuthTest = mobileAuthTest
}

export default mobileAuthTest
