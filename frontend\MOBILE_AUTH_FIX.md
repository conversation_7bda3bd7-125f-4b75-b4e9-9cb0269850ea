# 移动端认证问题修复说明

## 问题描述

在移动端页面中，当用户登录状态过期或未登录时，点击其他接口（如签到）不会自动跳转到登录页面，而是显示错误提示。这是因为移动端使用了独立的认证系统，但缺少统一的401错误处理机制。

## 问题原因

1. **认证系统分离**：移动端使用钉钉登录，token存储在localStorage中，而PC端使用cookies
2. **axios拦截器不兼容**：原有的axios拦截器只处理PC端的token管理
3. **API调用混乱**：移动端页面中混用了`request`和`mobileRequest`
4. **缺少统一的401处理**：没有统一的token过期处理和登录跳转机制

## 解决方案

### 1. 创建移动端专用的axios实例 (`frontend/src/utils/mobileRequest.js`)

- 创建独立的axios实例，专门处理移动端请求
- 实现完整的请求/响应拦截器
- 添加token自动刷新机制
- 实现401错误的统一处理和登录跳转

### 2. 增强用户store (`frontend/src/store/mobile/user.js`)

- 添加`redirectToLogin`方法，统一处理登录跳转
- 保存当前路径，登录成功后自动返回

### 3. 启用路由守卫 (`frontend/src/permission.js`)

- 启用移动端页面的登录检查
- 未登录用户自动跳转到钉钉登录页

### 4. 修复移动端页面 (`frontend/src/views/wel/mobile.vue`)

- 使用`mobileRequest`替代直接的`request`调用
- 增强登录状态检查逻辑

## 主要修改文件

1. `frontend/src/utils/mobileRequest.js` - 移动端专用HTTP请求工具
2. `frontend/src/store/mobile/user.js` - 移动端用户状态管理
3. `frontend/src/permission.js` - 路由守卫配置
4. `frontend/src/views/wel/mobile.vue` - 移动端主页面
5. `frontend/src/utils/mobile-auth-test.js` - 测试工具（新增）

## 功能特性

### 自动token刷新
- 当API返回401错误时，自动尝试刷新token
- 使用队列机制避免并发刷新
- 刷新失败时自动跳转登录页

### 统一错误处理
- 401错误自动跳转登录页
- 保存当前路径，登录后自动返回
- 显示友好的错误提示

### 登录状态检查
- 页面加载时检查登录状态
- 路由守卫拦截未登录访问
- 支持从localStorage恢复登录状态

## 测试方法

### 1. 使用开发工具测试
访问 `/dev-tools` 页面，点击"打开控制台测试工具"，然后在控制台中使用：

```javascript
// 检查登录状态
mobileAuthTest.check()

// 测试API调用
mobileAuthTest.testApi()

// 测试token刷新
mobileAuthTest.testRefresh()

// 运行完整测试
mobileAuthTest.test()

// 清除登录数据
mobileAuthTest.clear()
```

### 2. 手动测试流程

1. **清除登录状态**：在控制台执行 `mobileAuthTest.clear()`
2. **访问移动端页面**：访问 `/mobile`，应该自动跳转到 `/dinglogin`
3. **完成登录**：在钉钉登录页面完成登录
4. **测试API调用**：点击签到等功能，应该正常工作
5. **模拟token过期**：在控制台执行 `mobileAuthTest.expireToken()`
6. **测试自动刷新**：再次点击API功能，应该自动刷新token

## 注意事项

1. **环境变量**：确保钉钉相关的环境变量已正确配置
2. **后端接口**：确保后端的token刷新接口正常工作
3. **CORS配置**：确保跨域请求配置正确
4. **错误处理**：在生产环境中可能需要更详细的错误日志

## 兼容性

- 保持PC端认证系统不变
- 移动端和PC端可以独立工作
- 不影响现有的API接口
