import { defineStore } from 'pinia'
import { ref } from 'vue'
import request from '@/axios'
import { useRouter } from 'vue-router'
import * as dd from 'dingtalk-jsapi'

export const useUserStore = defineStore('user', () => {
  const accessToken = ref(null)
  const refreshToken = ref(null)
  const isLogin = ref(false)
  const userInfo = ref(null)
  const router = useRouter()

  async function login() {
    return new Promise((resolve, reject) => {
      dd.getAuthCode({
        corpId: import.meta.env.VITE_DING_CORP_ID
      })
      .then((res) => {
        console.log(res.code)
        let authCode = res.code
        request({
          url: '/api/dingapp/user/login',
          method: 'post',
          data: {
            authCode: authCode
          }
        }).then((res) => {
          console.log('登录成功', res)
          // 根据项目的axios配置，res可能是完整响应对象或已提取的数据
          // 如果res有data属性，说明是完整响应对象，需要提取data
          const responseData = res.data ? res.data : res
          handleLoginSuccess(responseData)
          resolve()
        }).catch((err) => {
          console.log(err)
          console.log('登录失败')
          reject(err)
        })
      })
    })
  }

  function handleLoginSuccess(response, redirect) {
    accessToken.value = response.access_token
    refreshToken.value = response.refresh_token
    isLogin.value = true

    // 存储用户信息
    userInfo.value = response

    // 将用户信息保存到localStorage，以便刷新页面后恢复
    localStorage.setItem('userInfo', JSON.stringify(userInfo.value))

    startRefreshTokenTask()

    // 立即执行路由跳转，不使用setTimeout
    if (redirect) {
      router.replace(redirect)
    } else {
      router.replace('/mobile')
    }
  }

  // 刷新token方法
  async function refreshAccessToken() {
    if (!refreshToken.value) {
      throw new Error('没有refresh token')
    }

    const formData = new FormData();
    formData.append('grant_type', 'refresh_token');
    formData.append('refresh_token', refreshToken.value);
    formData.append('scope', 'all');
    formData.append('tenantId', '000000');

    const res = await request({
      method: 'POST',
      url: '/api/blade-auth/oauth/token',
      data: formData,
      headers: {
        'Tenant-Id': '000000',
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    });

    // 根据项目的axios配置，res可能是完整响应对象或已提取的数据
    const responseData = res.data ? res.data : res

    // 更新token
    accessToken.value = responseData.access_token
    refreshToken.value = responseData.refresh_token

    // 更新localStorage中的用户信息
    if (userInfo.value) {
      userInfo.value.access_token = responseData.access_token
      userInfo.value.refresh_token = responseData.refresh_token
      localStorage.setItem('userInfo', JSON.stringify(userInfo.value))
    }

    return responseData.access_token
  }

  function startRefreshTokenTask() {
    setInterval(async () => {
      if (!isLogin.value) return

      try {
        await refreshAccessToken()
        console.log('定时刷新token成功')
      } catch (error) {
        console.error('定时刷新token失败:', error)
      }
    }, 55 * 60 * 1000) // 55分钟
  }

  // 初始化时从localStorage恢复用户信息
  function initUserInfo() {
    const storedUserInfo = localStorage.getItem('userInfo')
    if (storedUserInfo) {
      try {
        const parsedUserInfo = JSON.parse(storedUserInfo)
        userInfo.value = parsedUserInfo

        // 恢复token信息
        if (parsedUserInfo.access_token) {
          accessToken.value = parsedUserInfo.access_token
          refreshToken.value = parsedUserInfo.refresh_token
          isLogin.value = true

          // 启动定时刷新任务
          startRefreshTokenTask()
        }
      } catch (e) {
        console.error('解析用户信息失败:', e)
        // 清除损坏的数据
        localStorage.removeItem('userInfo')
      }
    }
  }

  // 清除用户信息
  function clearUserInfo() {
    userInfo.value = null
    accessToken.value = null
    refreshToken.value = null
    isLogin.value = false
    localStorage.removeItem('userInfo')
  }

  // 跳转到登录页
  function redirectToLogin() {
    // 保存当前路径
    const currentPath = window.location.pathname + window.location.search
    localStorage.setItem('redirectPath', currentPath)

    // 清除用户信息
    clearUserInfo()

    // 跳转到钉钉登录页
    setTimeout(() => {
      if (router) {
        router.replace('/dinglogin')
      } else {
        window.location.href = '/dinglogin'
      }
    }, 100)
  }

  // 初始化用户信息
  initUserInfo()

  return {
    accessToken,
    refreshToken,
    isLogin,
    userInfo,
    login,
    handleLoginSuccess,
    clearUserInfo,
    refreshAccessToken,
    initUserInfo,
    redirectToLogin
  }
})